# Devoir Unity 2D - Création d'une Carte de Jeu

## 1. Description du Jeu

### Concept général
**Titre** : "Forest Adventure"
**Genre** : Platformer 2D avec éléments d'exploration
**Thème** : Aventure dans une forêt mystique

### Histoire
Le joueur incarne un petit aventurier qui doit traverser une forêt enchantée pour atteindre un trésor caché. Le niveau comprend des plateformes naturelles, des arbres à escalader, et des obstacles à éviter.

### Mécaniques de jeu
- Déplacement horizontal et vertical (WASD ou flèches)
- Saut entre les plateformes
- Collecte d'objets (optionnel)
- Évitement d'obstacles

### Objectif du niveau
Atteindre la sortie du niveau située en haut à droite de la carte, en naviguant à travers les plateformes et obstacles de la forêt.

## 2. Ressources Utilisées

### Sources
- **Sprites fournis** : Ressources fournies par l'enseignant dans le cadre du cours
- **Arrière-plan** : Ciel avec nuages (style cartoon)
- **Personnages** : Sprites d'aventuriers avec animations
- **Environnement** : Arbres, plateformes en bois, herbe, rochers

### Organisation
Les ressources sont organisées dans le dossier `Assets/Art/` avec les sous-dossiers :
- `Characters/` : Sprites des personnages
- `Environment/` : Éléments de décor
- `Platforms/` : Plateformes et terrain
- `Backgrounds/` : Arrière-plans

## 3. Processus de Création

### Étape 1 : Préparation du projet
- [x] Création de la structure de dossiers
- [x] Import des ressources graphiques
- [ ] Configuration des sprites dans Unity

### Étape 2 : Configuration des couches (Layers)
- [ ] Background Layer (Arrière-plan)
- [ ] Environment Layer (Décors lointains)
- [ ] Platform Layer (Plateformes de jeu)
- [ ] Player Layer (Personnage joueur)
- [ ] Foreground Layer (Éléments au premier plan)

### Étape 3 : Création de la carte
- [ ] Mise en place du système Tilemap
- [ ] Création du terrain de base
- [ ] Placement des plateformes
- [ ] Ajout des éléments de décor

### Étape 4 : Intégration du joueur
- [ ] Configuration du prefab Player
- [ ] Test des déplacements
- [ ] Ajustement des collisions

## 4. Captures d'écran et Documentation

[Les captures d'écran seront ajoutées au fur et à mesure du développement]

## 5. Défis Rencontrés et Solutions

[À compléter pendant le développement]

## 6. Conclusion

[À rédiger à la fin du projet]
