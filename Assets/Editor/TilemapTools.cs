using UnityEditor;
using UnityEngine;
using UnityEngine.Tilemaps;

public static class TilemapTools
{
    [MenuItem("Tools/Tilemap/InitProject")]
    public static void InitProject()
    {
        var grid = GameObject.FindObjectOfType<Grid>();
        if (grid == null)
        {
            grid = new GameObject("Grid", typeof(Grid)).GetComponent<Grid>();
        }

        var tilemapObj = GameObject.Find("Tilemap");
        if (tilemapObj == null)
        {
            tilemapObj = new GameObject("Tilemap", typeof(Tilemap), typeof(TilemapRenderer));
            tilemapObj.transform.SetParent(grid.transform);
        }

        Debug.Log("✔ Grid + Tilemap créées ou trouvées.");
    }
}
